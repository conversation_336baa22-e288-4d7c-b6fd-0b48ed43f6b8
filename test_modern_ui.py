#!/usr/bin/env python3
"""
Test the modern UI components
"""

import tkinter as tk
try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    print("CustomTkinter is available and configured")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False
    print("CustomTkinter not available")

def test_modern_ui():
    """Test the modern UI"""
    print("Creating test window...")
    
    if CUSTOMTKINTER_AVAILABLE:
        root = ctk.CTk()
        root.title("Modern UI Test")
        root.geometry("800x600")
        
        # Create tabview
        tabview = ctk.CTkTabview(root)
        tabview.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Add tabs
        tab1 = tabview.add("Tab 1")
        tab2 = tabview.add("Tab 2")
        
        # Add content to tabs
        label1 = ctk.CTkLabel(tab1, text="This is Tab 1", font=ctk.CTkFont(size=16))
        label1.pack(pady=20)
        
        button1 = ctk.CTkButton(tab1, text="Test Button")
        button1.pack(pady=10)
        
        label2 = ctk.CTkLabel(tab2, text="This is Tab 2", font=ctk.CTkFont(size=16))
        label2.pack(pady=20)
        
        print("Modern UI created successfully")
        root.mainloop()
    else:
        print("CustomTkinter not available, using regular tkinter")
        root = tk.Tk()
        root.title("Fallback UI Test")
        root.geometry("800x600")
        
        label = tk.Label(root, text="CustomTkinter not available")
        label.pack(pady=50)
        
        root.mainloop()

if __name__ == "__main__":
    test_modern_ui()
