#!/usr/bin/env python3
"""
Test CustomTkinter installation
"""

try:
    import customtkinter as ctk
    print("CustomTkinter imported successfully!")
    print("Version:", ctk.__version__)
    
    # Create a simple test window
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    app = ctk.CTk()
    app.title("CustomTkinter Test")
    app.geometry("400x300")
    
    label = ctk.CTkLabel(app, text="CustomTkinter is working!", font=ctk.CTkFont(size=20))
    label.pack(pady=50)
    
    button = ctk.CTkButton(app, text="Close", command=app.quit)
    button.pack(pady=20)
    
    print("Starting CustomTkinter test window...")
    app.mainloop()
    
except ImportError as e:
    print(f"Failed to import CustomTkinter: {e}")
except Exception as e:
    print(f"Error: {e}")
