#!/usr/bin/env python3
"""
Minimal test of the modernized Attribution Dashboard Customizer
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add the current directory to the path to import from attrcust
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Modern UI imports
try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    # Configure CustomTkinter appearance
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    print("CustomTkinter loaded successfully")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False
    print("CustomTkinter not available")

class MinimalDashboardCustomizer:
    """Minimal version to test the modern UI structure"""
    
    def __init__(self):
        print("Initializing minimal dashboard customizer...")
        
        # Use CustomTkinter if available, fallback to tkinter
        if CUSTOMTKINTER_AVAILABLE:
            self.root = ctk.CTk()
            self.root.title("Attribution Dashboard Customizer v1.0 - Modern (Test)")
            print("Using CustomTkinter")
        else:
            self.root = tk.Tk()
            self.root.title("Attribution Dashboard Customizer v1.0 - Fallback")
            print("Using regular tkinter")
        
        self.root.geometry("1000x700")
        self.root.minsize(800, 500)
        
        # Setup styles and create widgets
        self.setup_styles()
        self.create_widgets()
        self.setup_layout()
        
        print("Minimal dashboard customizer initialized successfully")
    
    def setup_styles(self):
        """Configure styles"""
        if CUSTOMTKINTER_AVAILABLE:
            self.title_font = ctk.CTkFont(family="Arial", size=16, weight="bold")
            self.heading_font = ctk.CTkFont(family="Arial", size=12, weight="bold")
            self.normal_font = ctk.CTkFont(family="Arial", size=10)
        else:
            # Fallback to ttk styles
            style = ttk.Style()
            style.theme_use('clam')
    
    def create_widgets(self):
        """Create all GUI widgets"""
        print("Creating widgets...")
        
        # Main notebook for tabs
        if CUSTOMTKINTER_AVAILABLE:
            self.notebook = ctk.CTkTabview(self.root)
            self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        else:
            self.notebook = ttk.Notebook(self.root)
        
        # Create tabs
        self.create_test_tabs()
        
        print("Widgets created successfully")
    
    def create_test_tabs(self):
        """Create test tabs"""
        if CUSTOMTKINTER_AVAILABLE:
            # Analysis tab
            analysis_frame = self.notebook.add("📁 File Analysis")
            
            # Header
            header_label = ctk.CTkLabel(analysis_frame, 
                                      text="File Analysis Tab (Modernized)", 
                                      font=self.title_font)
            header_label.pack(pady=20)
            
            # Test button
            test_btn = ctk.CTkButton(analysis_frame, 
                                   text="Test Modern Button",
                                   command=self.test_action,
                                   height=40)
            test_btn.pack(pady=10)
            
            # Test entry
            test_entry = ctk.CTkEntry(analysis_frame, 
                                    placeholder_text="Test modern entry...",
                                    width=300, height=35)
            test_entry.pack(pady=10)
            
            # Placeholder tabs
            for tab_name in ["🎨 Customization", "📊 Data Sources", "🔗 Airtable", "🚀 Deployment"]:
                tab_frame = self.notebook.add(tab_name)
                placeholder_label = ctk.CTkLabel(tab_frame, 
                                               text=f"{tab_name}\n\nThis tab will be modernized next...",
                                               font=self.heading_font)
                placeholder_label.pack(expand=True)
        else:
            # Fallback to regular tabs
            analysis_frame = ttk.Frame(self.notebook)
            self.notebook.add(analysis_frame, text="📁 File Analysis")
            
            ttk.Label(analysis_frame, text="File Analysis Tab (Fallback)", 
                     font=('Arial', 16, 'bold')).pack(pady=20)
            ttk.Button(analysis_frame, text="Test Button", 
                      command=self.test_action).pack(pady=10)
    
    def test_action(self):
        """Test action for buttons"""
        if CUSTOMTKINTER_AVAILABLE:
            messagebox.showinfo("Test", "Modern UI is working! CustomTkinter button clicked.")
        else:
            messagebox.showinfo("Test", "Fallback UI is working! Regular button clicked.")
    
    def setup_layout(self):
        """Setup the main layout"""
        # Pack notebook only if not using CustomTkinter (already packed in create_widgets)
        if not CUSTOMTKINTER_AVAILABLE:
            self.notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # Status bar
        if CUSTOMTKINTER_AVAILABLE:
            self.status_frame = ctk.CTkFrame(self.root, height=30)
            self.status_frame.pack(fill='x', side='bottom', padx=10, pady=(0, 10))
            
            status_label = ctk.CTkLabel(self.status_frame, 
                                      text="Modern UI Test - Ready", 
                                      font=self.normal_font)
            status_label.pack(side='left', padx=10, pady=5)
            
            version_label = ctk.CTkLabel(self.status_frame, 
                                       text="Dashboard Customizer v1.0 - Modern Test",
                                       font=ctk.CTkFont(family="Arial", size=8))
            version_label.pack(side='right', padx=10, pady=5)
        else:
            self.status_frame = ttk.Frame(self.root)
            self.status_frame.pack(fill='x', side='bottom')
            
            ttk.Label(self.status_frame, text="Fallback UI Test - Ready").pack(side='left', padx=5, pady=2)
            ttk.Label(self.status_frame, text="Dashboard Customizer v1.0 - Fallback",
                     font=('Arial', 8)).pack(side='right', padx=5, pady=2)
    
    def run(self):
        """Run the application"""
        print("Starting application main loop...")
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"Error in main loop: {e}")
            import traceback
            traceback.print_exc()

def main():
    """Main entry point"""
    try:
        print("Starting minimal Attribution Dashboard Customizer test...")
        app = MinimalDashboardCustomizer()
        app.run()
        print("Application finished")
    except Exception as e:
        print(f"Failed to start application: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
