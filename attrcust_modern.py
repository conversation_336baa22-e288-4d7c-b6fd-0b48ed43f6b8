#!/usr/bin/env python3
"""
Attribution Dashboard Customizer - Modern UI Version
A modern, responsive UI for configuring Attribution Dashboard clients with Airtable integration.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog
import json
import os
import logging
from datetime import datetime
import threading
import sys

# Import the existing functionality from the original file
try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("attrcust", "attrcust.py")
    attrcust_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(attrcust_module)

    AirtableManager = attrcust_module.AirtableAPIManager
    logger = attrcust_module.logger

except Exception as e:
    print(f"Error importing from attrcust.py: {e}")
    print("Creating minimal AirtableManager for testing...")

    # Create a minimal AirtableManager for testing
    class AirtableManager:
        def __init__(self):
            self.token = None

        def set_token(self, token):
            self.token = token

        def test_connection(self):
            if self.token:
                return True, [{"name": "Test Base", "id": "test123", "permission": "create"}]
            return False, "No token provided"

        def get_base_schema(self, base_id):
            return True, [{"name": "Test Table", "id": "tbl123"}]

        def create_table_in_base(self, base_id, template):
            return True, "Table created successfully"

        def _get_ghl_table_template(self):
            return {"name": "GHL", "fields": []}

        def _get_google_ads_table_template(self):
            return {"name": "Google Ads", "fields": []}

        def _get_pos_table_template(self):
            return {"name": "POS", "fields": []}

        def _get_meta_ads_table_template(self):
            return {"name": "Meta Ads", "fields": []}

    import logging
    logger = logging.getLogger(__name__)

# Configure CustomTkinter appearance
ctk.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class ModernAttributionDashboardCustomizer:
    """Modern UI version of the Attribution Dashboard Customizer"""
    
    def __init__(self):
        # Initialize the main window
        self.root = ctk.CTk()
        self.root.title("Attribution Dashboard Customizer v2.0")
        self.root.geometry("1200x800")
        self.root.minsize(900, 600)
        
        # Initialize managers
        self.airtable_manager = AirtableManager()
        self.selected_base_data = None
        
        # Create the modern UI
        self.create_modern_ui()

        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()

        # Setup logging display
        self.setup_logging()
        
    def create_modern_ui(self):
        """Create the modern UI layout"""
        # Configure main grid
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(0, weight=1)
        
        # Create main container
        self.main_container = ctk.CTkFrame(self.root, corner_radius=0)
        self.main_container.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
        
        # Configure main container grid
        self.main_container.grid_columnconfigure(1, weight=1)
        self.main_container.grid_rowconfigure(0, weight=1)
        
        # Create sidebar
        self.create_sidebar()
        
        # Create main content area
        self.create_main_content()
        
    def create_sidebar(self):
        """Create the modern sidebar navigation"""
        self.sidebar = ctk.CTkFrame(self.main_container, width=250, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
        self.sidebar.grid_propagate(False)
        
        # Configure sidebar grid
        self.sidebar.grid_columnconfigure(0, weight=1)
        
        # App title
        title_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        title_frame.grid(row=0, column=0, sticky="ew", padx=20, pady=(20, 10))
        
        app_title = ctk.CTkLabel(
            title_frame,
            text="Attribution Dashboard",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        app_title.pack()
        
        subtitle = ctk.CTkLabel(
            title_frame,
            text="Customizer v2.0",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        subtitle.pack()
        
        # Navigation buttons
        nav_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        nav_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=10)
        nav_frame.grid_columnconfigure(0, weight=1)
        
        # Navigation sections
        self.nav_buttons = {}
        nav_items = [
            ("🏠", "Dashboard", "dashboard"),
            ("🔗", "Airtable Setup", "airtable"),
            ("⚙️", "Configuration", "config"),
            ("📊", "Generate", "generate"),
            ("📋", "Logs", "logs"),
            ("⚙️", "Settings", "settings")
        ]
        
        for i, (icon, text, key) in enumerate(nav_items):
            btn = ctk.CTkButton(
                nav_frame,
                text=f"{icon}  {text}",
                command=lambda k=key: self.switch_tab(k),
                height=40,
                anchor="w",
                fg_color="transparent",
                text_color=("gray10", "gray90"),
                hover_color=("gray80", "gray20")
            )
            btn.grid(row=i, column=0, sticky="ew", pady=2)
            self.nav_buttons[key] = btn
        
        # Theme toggle section
        theme_frame = ctk.CTkFrame(self.sidebar)
        theme_frame.grid(row=2, column=0, sticky="ew", padx=20, pady=(10, 10))

        theme_title = ctk.CTkLabel(
            theme_frame,
            text="Appearance",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        theme_title.pack(pady=(10, 5))

        # Theme toggle switch
        self.theme_switch = ctk.CTkSwitch(
            theme_frame,
            text="Dark Mode",
            command=self.toggle_theme,
            font=ctk.CTkFont(size=12)
        )
        self.theme_switch.pack(pady=(0, 10))
        self.theme_switch.select()  # Start with dark mode

        # Status section
        status_frame = ctk.CTkFrame(self.sidebar)
        status_frame.grid(row=3, column=0, sticky="ew", padx=20, pady=(10, 10))

        status_title = ctk.CTkLabel(
            status_frame,
            text="Connection Status",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        status_title.pack(pady=(10, 5))

        self.status_label = ctk.CTkLabel(
            status_frame,
            text="🔴 Not Connected",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=(0, 10))

        # Quick stats section
        stats_frame = ctk.CTkFrame(self.sidebar)
        stats_frame.grid(row=4, column=0, sticky="ew", padx=20, pady=(10, 20))

        stats_title = ctk.CTkLabel(
            stats_frame,
            text="Quick Stats",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        stats_title.pack(pady=(10, 5))

        self.stats_label = ctk.CTkLabel(
            stats_frame,
            text="📊 Bases: 0\n🔧 Configured: 0",
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        self.stats_label.pack(pady=(0, 10))
        
    def create_main_content(self):
        """Create the main content area with tabs"""
        self.content_frame = ctk.CTkFrame(self.main_container, corner_radius=0)
        self.content_frame.grid(row=0, column=1, sticky="nsew", padx=0, pady=0)
        
        # Configure content grid
        self.content_frame.grid_columnconfigure(0, weight=1)
        self.content_frame.grid_rowconfigure(0, weight=1)
        
        # Create tab content frames
        self.tab_frames = {}
        
        # Dashboard tab
        self.create_dashboard_tab()
        
        # Airtable setup tab
        self.create_airtable_tab()
        
        # Configuration tab
        self.create_config_tab()
        
        # Generate tab
        self.create_generate_tab()
        
        # Logs tab
        self.create_logs_tab()

        # Settings tab
        self.create_settings_tab()

        # Show dashboard by default
        self.switch_tab("dashboard")
        
    def create_dashboard_tab(self):
        """Create the dashboard overview tab"""
        frame = ctk.CTkScrollableFrame(self.content_frame)
        self.tab_frames["dashboard"] = frame
        
        # Welcome section
        welcome_frame = ctk.CTkFrame(frame)
        welcome_frame.pack(fill="x", padx=20, pady=20)
        
        welcome_title = ctk.CTkLabel(
            welcome_frame,
            text="Welcome to Attribution Dashboard Customizer",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        welcome_title.pack(pady=(20, 10))
        
        welcome_desc = ctk.CTkLabel(
            welcome_frame,
            text="Configure your Airtable bases for the Attribution Dashboard.\nConnect your data sources and generate client configurations.",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        welcome_desc.pack(pady=(0, 20))
        
        # Quick actions
        actions_frame = ctk.CTkFrame(frame)
        actions_frame.pack(fill="x", padx=20, pady=10)
        
        actions_title = ctk.CTkLabel(
            actions_frame,
            text="Quick Actions",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        actions_title.pack(pady=(15, 10))
        
        # Action buttons grid
        buttons_frame = ctk.CTkFrame(actions_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Configure buttons grid
        for i in range(3):
            buttons_frame.grid_columnconfigure(i, weight=1)
        
        quick_actions = [
            ("🔗 Connect Airtable", lambda: self.switch_tab("airtable")),
            ("⚙️ Configure Base", lambda: self.switch_tab("config")),
            ("📊 Generate Config", lambda: self.switch_tab("generate"))
        ]
        
        for i, (text, command) in enumerate(quick_actions):
            btn = ctk.CTkButton(
                buttons_frame,
                text=text,
                command=command,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold")
            )
            btn.grid(row=0, column=i, padx=10, pady=5, sticky="ew")
            
    def create_airtable_tab(self):
        """Create the Airtable setup tab"""
        frame = ctk.CTkScrollableFrame(self.content_frame)
        self.tab_frames["airtable"] = frame
        
        # Header
        header_frame = ctk.CTkFrame(frame)
        header_frame.pack(fill="x", padx=20, pady=20)
        
        header_title = ctk.CTkLabel(
            header_frame,
            text="Airtable Configuration",
            font=ctk.CTkFont(size=22, weight="bold")
        )
        header_title.pack(pady=(15, 5))
        
        header_desc = ctk.CTkLabel(
            header_frame,
            text="Connect to your Airtable account and manage your bases",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        header_desc.pack(pady=(0, 15))
        
        # API Token section
        token_frame = ctk.CTkFrame(frame)
        token_frame.pack(fill="x", padx=20, pady=10)
        
        token_title = ctk.CTkLabel(
            token_frame,
            text="API Token",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        token_title.pack(pady=(15, 10), anchor="w", padx=20)
        
        # Token input with modern styling
        token_input_frame = ctk.CTkFrame(token_frame, fg_color="transparent")
        token_input_frame.pack(fill="x", padx=20, pady=(0, 15))
        token_input_frame.grid_columnconfigure(0, weight=1)
        
        self.token_entry = ctk.CTkEntry(
            token_input_frame,
            placeholder_text="Enter your Airtable API token...",
            height=40,
            font=ctk.CTkFont(size=12),
            show="*"
        )
        self.token_entry.grid(row=0, column=0, sticky="ew", padx=(0, 10))
        
        self.test_btn = ctk.CTkButton(
            token_input_frame,
            text="Test Connection",
            command=self.test_airtable_connection,
            width=140,
            height=40
        )
        self.test_btn.grid(row=0, column=1)

        # Progress bar for loading states
        self.progress_bar = ctk.CTkProgressBar(
            token_input_frame,
            width=400,
            height=8
        )
        self.progress_bar.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.progress_bar.set(0)  # Start at 0%
        
        # Bases section
        bases_frame = ctk.CTkFrame(frame)
        bases_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        bases_title = ctk.CTkLabel(
            bases_frame,
            text="Available Bases",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        bases_title.pack(pady=(15, 10), anchor="w", padx=20)
        
        # Bases list with modern table-like appearance
        self.bases_list_frame = ctk.CTkScrollableFrame(bases_frame, height=300)
        self.bases_list_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
    def switch_tab(self, tab_name):
        """Switch between different tabs"""
        # Hide all tabs
        for frame in self.tab_frames.values():
            frame.grid_remove()
        
        # Show selected tab
        if tab_name in self.tab_frames:
            self.tab_frames[tab_name].grid(row=0, column=0, sticky="nsew")
        
        # Update navigation button states
        for key, btn in self.nav_buttons.items():
            if key == tab_name:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color="transparent")
                
    def test_airtable_connection(self):
        """Test the Airtable connection with modern feedback"""
        token = self.token_entry.get().strip()
        if not token:
            self.show_modern_message("Error", "Please enter an API token", "error")
            return

        # Show loading state
        self.status_label.configure(text="🟡 Connecting...")
        self.test_btn.configure(text="Connecting...", state="disabled")
        self.progress_bar.set(0.1)
        self.root.update()

        # Test connection in thread to prevent UI freezing
        def test_connection():
            try:
                # Set token and test
                self.root.after(0, lambda: self.progress_bar.set(0.3))
                self.airtable_manager.api_token = token
                self.airtable_manager.headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }

                self.root.after(0, lambda: self.progress_bar.set(0.6))
                success, result = self.airtable_manager.test_connection()

                if success:
                    # Get the list of bases
                    self.root.after(0, lambda: self.progress_bar.set(0.9))
                    bases = self.airtable_manager.list_bases()
                    self.root.after(0, self.connection_result, True, bases)
                else:
                    self.root.after(0, self.connection_result, False, result)

            except Exception as e:
                self.root.after(0, self.connection_result, False, str(e))

        threading.Thread(target=test_connection, daemon=True).start()
        
    def connection_result(self, success, result):
        """Handle connection test result"""
        # Reset button and progress bar
        self.test_btn.configure(text="Test Connection", state="normal")

        if success:
            self.progress_bar.set(1.0)  # Complete
            self.status_label.configure(text="🟢 Connected")
            self.show_modern_message("Success", f"Connected successfully! Found {len(result)} bases.", "success")
            self.load_bases(result)
            self.update_stats(bases_count=len(result))
            self.log_message(f"Successfully connected to Airtable. Found {len(result)} bases.")
        else:
            self.progress_bar.set(0)  # Reset
            self.status_label.configure(text="🔴 Connection Failed")
            self.show_modern_message("Connection Failed", f"Error: {result}", "error")
            self.log_message(f"Connection failed: {result}")
            
    def show_modern_message(self, title, message, msg_type="info"):
        """Show a modern styled message dialog"""
        if msg_type == "success":
            messagebox.showinfo(title, message)
        elif msg_type == "error":
            messagebox.showerror(title, message)
        else:
            messagebox.showinfo(title, message)
            
    def load_bases(self, bases):
        """Load bases into the modern list view"""
        # Clear existing bases
        for widget in self.bases_list_frame.winfo_children():
            widget.destroy()
            
        if not bases:
            no_bases_label = ctk.CTkLabel(
                self.bases_list_frame,
                text="No bases found",
                text_color=("gray60", "gray40")
            )
            no_bases_label.pack(pady=20)
            return
            
        # Create modern base cards
        for base in bases:
            self.create_base_card(base)
            
    def create_base_card(self, base):
        """Create a modern card for each base"""
        card = ctk.CTkFrame(self.bases_list_frame)
        card.pack(fill="x", padx=5, pady=5)
        
        # Configure card grid
        card.grid_columnconfigure(1, weight=1)
        
        # Base icon
        icon_label = ctk.CTkLabel(
            card,
            text="📊",
            font=ctk.CTkFont(size=24)
        )
        icon_label.grid(row=0, column=0, rowspan=2, padx=15, pady=15)
        
        # Base name
        name_label = ctk.CTkLabel(
            card,
            text=base['name'],
            font=ctk.CTkFont(size=16, weight="bold"),
            anchor="w"
        )
        name_label.grid(row=0, column=1, sticky="ew", padx=(0, 15), pady=(15, 5))
        
        # Base info
        info_text = f"ID: {base['id']} • Permission: {base.get('permission', 'N/A')}"
        info_label = ctk.CTkLabel(
            card,
            text=info_text,
            font=ctk.CTkFont(size=12),
            text_color=("gray60", "gray40"),
            anchor="w"
        )
        info_label.grid(row=1, column=1, sticky="ew", padx=(0, 15), pady=(0, 15))
        
        # Select button
        select_btn = ctk.CTkButton(
            card,
            text="Select",
            command=lambda b=base: self.select_base(b),
            width=80,
            height=30
        )
        select_btn.grid(row=0, column=2, rowspan=2, padx=15, pady=15)
        
    def select_base(self, base):
        """Select a base for configuration"""
        self.selected_base_data = base
        self.show_modern_message("Base Selected", f"Selected: {base['name']}", "success")

        # Update selected base info in config tab
        self.update_selected_base_info()

        # Switch to configuration tab
        self.switch_tab("config")

    def update_selected_base_info(self):
        """Update the selected base information display"""
        if self.selected_base_data:
            info_text = f"📊 {self.selected_base_data['name']}\n"
            info_text += f"ID: {self.selected_base_data['id']}\n"
            info_text += f"Permission: {self.selected_base_data.get('permission', 'N/A')}"
            self.selected_base_info.configure(text=info_text)
        else:
            self.selected_base_info.configure(
                text="No base selected. Please go to Airtable Setup and select a base."
            )

    def check_and_configure_base(self):
        """Check the selected base and configure missing tables"""
        if not self.selected_base_data:
            self.show_modern_message("No Base Selected", "Please select a base first from the Airtable Setup tab.", "error")
            return

        # Clear results
        self.results_text.delete("1.0", "end")
        self.results_text.insert("1.0", "🔍 Checking base structure...\n\n")
        self.root.update()

        def check_base():
            try:
                base_id = self.selected_base_data['id']
                base_name = self.selected_base_data['name']

                # Get current tables
                success, current_tables = self.airtable_manager.get_base_schema(base_id)
                if not success:
                    self.root.after(0, self.show_check_error, f"Failed to get base schema: {current_tables}")
                    return

                # Check for required tables
                result = self.analyze_base_structure(current_tables)
                self.root.after(0, self.show_check_results, result)

            except Exception as e:
                self.root.after(0, self.show_check_error, str(e))

        threading.Thread(target=check_base, daemon=True).start()

    def analyze_base_structure(self, current_tables):
        """Analyze the base structure and return results"""
        existing_table_names = [table['name'].lower() for table in current_tables]

        # Define required tables
        required_tables = {
            'ghl': {
                'check_names': ['ghl', 'gohighlevel', 'leads'],
                'default_name': 'GHL'
            },
            'google_ads': {
                'check_names': ['google ads', 'google_ads', 'googleads', 'adwords'],
                'default_name': 'Google Ads'
            },
            'pos': {
                'check_names': ['pos', 'point of sale', 'transactions', 'sales'],
                'default_name': 'POS'
            },
            'meta_ads': {
                'check_names': ['meta ads', 'meta_ads', 'facebook ads', 'fb ads'],
                'default_name': 'Meta Ads'
            }
        }

        # Check which tables exist and which are missing
        existing_tables = []
        missing_tables = []

        for table_type, table_config in required_tables.items():
            found = False
            for check_name in table_config['check_names']:
                if any(check_name in existing_name for existing_name in existing_table_names):
                    existing_tables.append(table_config['default_name'])
                    found = True
                    break

            if not found:
                missing_tables.append(table_config['default_name'])

        return {
            'total_tables': len(current_tables),
            'existing_tables': existing_tables,
            'missing_tables': missing_tables,
            'all_tables': [table['name'] for table in current_tables]
        }

    def show_check_results(self, result):
        """Display the check results in the UI"""
        self.results_text.delete("1.0", "end")

        # Header
        self.results_text.insert("end", "✅ Base Analysis Complete\n")
        self.results_text.insert("end", "=" * 50 + "\n\n")

        # Summary
        self.results_text.insert("end", f"📊 Total Tables Found: {result['total_tables']}\n\n")

        # Existing Attribution Dashboard tables
        if result['existing_tables']:
            self.results_text.insert("end", "✅ Existing Attribution Dashboard Tables:\n")
            for table in result['existing_tables']:
                self.results_text.insert("end", f"   • {table}\n")
            self.results_text.insert("end", "\n")

        # Missing tables
        if result['missing_tables']:
            self.results_text.insert("end", "❌ Missing Attribution Dashboard Tables:\n")
            for table in result['missing_tables']:
                self.results_text.insert("end", f"   • {table}\n")
            self.results_text.insert("end", "\n")

            # Ask if user wants to create missing tables
            self.results_text.insert("end", "💡 Would you like to create the missing tables?\n")
            self.results_text.insert("end", "Click 'Create Missing Tables' button below.\n\n")

            # Add create button
            self.show_create_tables_button(result['missing_tables'])
        else:
            self.results_text.insert("end", "🎉 All required Attribution Dashboard tables are present!\n\n")

        # All tables list
        self.results_text.insert("end", "📋 All Tables in Base:\n")
        for table in result['all_tables']:
            self.results_text.insert("end", f"   • {table}\n")

    def show_create_tables_button(self, missing_tables):
        """Show button to create missing tables"""
        # Create a frame for the button inside the results area
        button_frame = ctk.CTkFrame(self.results_frame, fg_color="transparent")
        button_frame.pack(pady=10)

        create_btn = ctk.CTkButton(
            button_frame,
            text=f"🔧 Create {len(missing_tables)} Missing Tables",
            command=lambda: self.create_missing_tables(missing_tables),
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#1f538d", "#14375e")
        )
        create_btn.pack()

    def create_missing_tables(self, missing_tables):
        """Create the missing tables"""
        self.results_text.insert("end", f"\n🔧 Creating {len(missing_tables)} missing tables...\n\n")
        self.root.update()

        def create_tables():
            try:
                # Import table templates from original file
                from attrcust import AirtableManager

                tables_created = []
                base_id = self.selected_base_data['id']

                # Map table names to template methods
                template_map = {
                    'GHL': self.airtable_manager._get_ghl_table_template,
                    'Google Ads': self.airtable_manager._get_google_ads_table_template,
                    'POS': self.airtable_manager._get_pos_table_template,
                    'Meta Ads': self.airtable_manager._get_meta_ads_table_template
                }

                for table_name in missing_tables:
                    if table_name in template_map:
                        try:
                            template = template_map[table_name]()
                            template['name'] = table_name

                            success, result = self.airtable_manager.create_table_in_base(base_id, template)

                            if success:
                                tables_created.append(table_name)
                                self.root.after(0, self.log_table_creation, table_name, True)
                            else:
                                self.root.after(0, self.log_table_creation, table_name, False, result)

                        except Exception as e:
                            self.root.after(0, self.log_table_creation, table_name, False, str(e))

                self.root.after(0, self.creation_complete, tables_created)

            except Exception as e:
                self.root.after(0, self.show_check_error, f"Error creating tables: {str(e)}")

        threading.Thread(target=create_tables, daemon=True).start()

    def log_table_creation(self, table_name, success, error=None):
        """Log table creation result"""
        if success:
            self.results_text.insert("end", f"✅ Created: {table_name}\n")
        else:
            self.results_text.insert("end", f"❌ Failed to create {table_name}: {error}\n")
        self.root.update()

    def creation_complete(self, tables_created):
        """Handle completion of table creation"""
        if tables_created:
            self.results_text.insert("end", f"\n🎉 Successfully created {len(tables_created)} tables!\n")
            self.show_modern_message("Success", f"Created {len(tables_created)} tables successfully!", "success")
        else:
            self.results_text.insert("end", "\n❌ No tables were created. Check the logs for details.\n")
            self.show_modern_message("Warning", "No tables were created. Check the results for details.", "error")

    def show_check_error(self, error_msg):
        """Show error in check results"""
        self.results_text.delete("1.0", "end")
        self.results_text.insert("1.0", f"❌ Error: {error_msg}\n")

    def refresh_base_info(self):
        """Refresh the selected base information"""
        if self.selected_base_data:
            self.check_and_configure_base()
        else:
            self.show_modern_message("No Base Selected", "Please select a base first.", "error")
        
    def generate_configuration(self):
        """Generate configuration for the selected base"""
        if not self.selected_base_data:
            self.show_modern_message("No Base Selected", "Please select a base first.", "error")
            return

        try:
            base_info = self.selected_base_data
            base_name = base_info['name']

            # Generate configuration using the original method
            config = {
                "client_info": {
                    "client_id": base_name.lower().replace(' ', '_'),
                    "business_name": base_name,
                    "created_date": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                },
                "airtable": {
                    "base_id": base_info['id'],
                    "base_name": base_name,
                    "tables": {
                        "ghl_leads": "GHL",
                        "google_ads": "Google Ads",
                        "pos_data": "POS",
                        "meta_ads": "Meta Ads"
                    }
                },
                "dashboard_settings": {
                    "enabled_tabs": ["ghl", "google_ads"],
                    "theme": "modern",
                    "auto_refresh": True,
                    "refresh_interval": 300
                }
            }

            # Display in preview
            config_json = json.dumps(config, indent=2)
            self.config_preview.delete("1.0", "end")
            self.config_preview.insert("1.0", config_json)

            self.current_config = config
            self.show_modern_message("Success", "Configuration generated successfully!", "success")

        except Exception as e:
            self.show_modern_message("Error", f"Failed to generate configuration: {str(e)}", "error")

    def save_configuration(self):
        """Save the generated configuration to file"""
        if not hasattr(self, 'current_config'):
            self.show_modern_message("No Configuration", "Please generate a configuration first.", "error")
            return

        try:
            # Ask user for save location
            filename = filedialog.asksaveasfilename(
                title="Save Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w') as f:
                    json.dump(self.current_config, f, indent=2)

                self.show_modern_message("Success", f"Configuration saved to {filename}", "success")
                self.log_message(f"Configuration saved to {filename}")

        except Exception as e:
            self.show_modern_message("Error", f"Failed to save configuration: {str(e)}", "error")

    def clear_logs(self):
        """Clear the logs display"""
        self.logs_text.delete("1.0", "end")

    def refresh_logs(self):
        """Refresh the logs display"""
        # This would typically read from a log file or logging handler
        self.log_message("Logs refreshed")

    def log_message(self, message):
        """Add a message to the logs"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.logs_text.insert("end", log_entry)
        self.logs_text.see("end")

    def toggle_theme(self):
        """Toggle between dark and light themes"""
        if self.theme_switch.get():
            # Dark mode
            ctk.set_appearance_mode("dark")
            self.theme_switch.configure(text="Dark Mode")
            self.log_message("Switched to dark mode")
        else:
            # Light mode
            ctk.set_appearance_mode("light")
            self.theme_switch.configure(text="Light Mode")
            self.log_message("Switched to light mode")

    def update_stats(self, bases_count=0, configured_count=0):
        """Update the quick stats display"""
        stats_text = f"📊 Bases: {bases_count}\n🔧 Configured: {configured_count}"
        self.stats_label.configure(text=stats_text)

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for common actions"""
        # Ctrl+1-5 for tab navigation
        self.root.bind("<Control-1>", lambda e: self.switch_tab("dashboard"))
        self.root.bind("<Control-2>", lambda e: self.switch_tab("airtable"))
        self.root.bind("<Control-3>", lambda e: self.switch_tab("config"))
        self.root.bind("<Control-4>", lambda e: self.switch_tab("generate"))
        self.root.bind("<Control-5>", lambda e: self.switch_tab("logs"))

        # Ctrl+T for test connection
        self.root.bind("<Control-t>", lambda e: self.test_airtable_connection())

        # Ctrl+G for generate configuration
        self.root.bind("<Control-g>", lambda e: self.generate_configuration())

        # Ctrl+S for save configuration
        self.root.bind("<Control-s>", lambda e: self.save_configuration())

        # Ctrl+R for refresh
        self.root.bind("<Control-r>", lambda e: self.refresh_base_info())

        # Ctrl+D for toggle dark mode
        self.root.bind("<Control-d>", lambda e: self.theme_switch.toggle())

        # F5 for refresh logs
        self.root.bind("<F5>", lambda e: self.refresh_logs())

        # Escape to clear logs
        self.root.bind("<Escape>", lambda e: self.clear_logs())

        # Add keyboard shortcut help
        self.log_message("Keyboard shortcuts enabled:")
        self.log_message("  Ctrl+1-5: Switch tabs")
        self.log_message("  Ctrl+T: Test connection")
        self.log_message("  Ctrl+G: Generate config")
        self.log_message("  Ctrl+S: Save config")
        self.log_message("  Ctrl+R: Refresh")
        self.log_message("  Ctrl+D: Toggle theme")
        self.log_message("  F5: Refresh logs")
        self.log_message("  Esc: Clear logs")

    def change_color_theme(self, theme):
        """Change the color theme"""
        ctk.set_default_color_theme(theme)
        self.log_message(f"Color theme changed to {theme}")
        self.show_modern_message("Theme Changed", f"Color theme changed to {theme}. Restart the app to see full effect.", "info")

    def reset_settings(self):
        """Reset all settings to defaults"""
        # Reset theme
        self.theme_option.set("blue")
        ctk.set_default_color_theme("blue")

        # Reset switches
        self.autosave_switch.select()
        self.debug_switch.deselect()
        self.verbose_switch.deselect()
        self.theme_switch.select()  # Dark mode

        # Reset timeout
        self.timeout_entry.delete(0, "end")
        self.timeout_entry.insert(0, "15")

        # Reset appearance
        ctk.set_appearance_mode("dark")

        self.log_message("Settings reset to defaults")
        self.show_modern_message("Settings Reset", "All settings have been reset to their default values.", "success")

    def export_settings(self):
        """Export application settings to a file"""
        try:
            settings = {
                "appearance": {
                    "theme": self.theme_option.get(),
                    "dark_mode": self.theme_switch.get()
                },
                "connection": {
                    "timeout": self.timeout_entry.get()
                },
                "preferences": {
                    "autosave": self.autosave_switch.get(),
                    "debug": self.debug_switch.get(),
                    "verbose": self.verbose_switch.get()
                },
                "export_date": datetime.now().isoformat(),
                "version": "2.0"
            }

            filename = filedialog.asksaveasfilename(
                title="Export Settings",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialname=f"attribution_dashboard_settings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )

            if filename:
                with open(filename, 'w') as f:
                    json.dump(settings, f, indent=2)

                self.show_modern_message("Export Successful", f"Settings exported to {filename}", "success")
                self.log_message(f"Settings exported to {filename}")

        except Exception as e:
            self.show_modern_message("Export Failed", f"Failed to export settings: {str(e)}", "error")

    def import_settings(self):
        """Import application settings from a file"""
        try:
            filename = filedialog.askopenfilename(
                title="Import Settings",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r') as f:
                    settings = json.load(f)

                # Apply settings
                if "appearance" in settings:
                    if "theme" in settings["appearance"]:
                        self.theme_option.set(settings["appearance"]["theme"])
                        ctk.set_default_color_theme(settings["appearance"]["theme"])

                    if "dark_mode" in settings["appearance"]:
                        if settings["appearance"]["dark_mode"]:
                            self.theme_switch.select()
                            ctk.set_appearance_mode("dark")
                        else:
                            self.theme_switch.deselect()
                            ctk.set_appearance_mode("light")

                if "connection" in settings and "timeout" in settings["connection"]:
                    self.timeout_entry.delete(0, "end")
                    self.timeout_entry.insert(0, str(settings["connection"]["timeout"]))

                if "preferences" in settings:
                    prefs = settings["preferences"]
                    if "autosave" in prefs:
                        if prefs["autosave"]:
                            self.autosave_switch.select()
                        else:
                            self.autosave_switch.deselect()

                    if "debug" in prefs:
                        if prefs["debug"]:
                            self.debug_switch.select()
                        else:
                            self.debug_switch.deselect()

                    if "verbose" in prefs:
                        if prefs["verbose"]:
                            self.verbose_switch.select()
                        else:
                            self.verbose_switch.deselect()

                self.show_modern_message("Import Successful", f"Settings imported from {filename}", "success")
                self.log_message(f"Settings imported from {filename}")

        except Exception as e:
            self.show_modern_message("Import Failed", f"Failed to import settings: {str(e)}", "error")

    def copy_to_clipboard(self):
        """Copy the current configuration to clipboard"""
        try:
            if hasattr(self, 'current_config'):
                config_text = json.dumps(self.current_config, indent=2)
                self.root.clipboard_clear()
                self.root.clipboard_append(config_text)
                self.show_modern_message("Copied", "Configuration copied to clipboard!", "success")
                self.log_message("Configuration copied to clipboard")
            else:
                self.show_modern_message("No Configuration", "Please generate a configuration first.", "error")
        except Exception as e:
            self.show_modern_message("Copy Failed", f"Failed to copy to clipboard: {str(e)}", "error")

    def setup_logging(self):
        """Setup logging to display in the logs tab"""
        # Add initial log message
        self.log_message("Application started")
        self.log_message("Modern UI initialized")
        self.log_message("Dark mode enabled by default")
        
    def create_config_tab(self):
        """Create the configuration tab"""
        frame = ctk.CTkScrollableFrame(self.content_frame)
        self.tab_frames["config"] = frame

        # Header
        header_frame = ctk.CTkFrame(frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        header_title = ctk.CTkLabel(
            header_frame,
            text="Base Configuration",
            font=ctk.CTkFont(size=22, weight="bold")
        )
        header_title.pack(pady=(15, 5))

        header_desc = ctk.CTkLabel(
            header_frame,
            text="Check and configure your selected base for Attribution Dashboard",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        header_desc.pack(pady=(0, 15))

        # Selected base info
        self.selected_base_frame = ctk.CTkFrame(frame)
        self.selected_base_frame.pack(fill="x", padx=20, pady=10)

        base_info_title = ctk.CTkLabel(
            self.selected_base_frame,
            text="Selected Base",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        base_info_title.pack(pady=(15, 10), anchor="w", padx=20)

        self.selected_base_info = ctk.CTkLabel(
            self.selected_base_frame,
            text="No base selected. Please go to Airtable Setup and select a base.",
            font=ctk.CTkFont(size=12),
            text_color=("gray60", "gray40")
        )
        self.selected_base_info.pack(pady=(0, 15), padx=20, anchor="w")

        # Configuration actions
        actions_frame = ctk.CTkFrame(frame)
        actions_frame.pack(fill="x", padx=20, pady=10)

        actions_title = ctk.CTkLabel(
            actions_frame,
            text="Configuration Actions",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        actions_title.pack(pady=(15, 10), anchor="w", padx=20)

        # Action buttons
        buttons_frame = ctk.CTkFrame(actions_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))

        self.check_config_btn = ctk.CTkButton(
            buttons_frame,
            text="🔍 Check & Configure Base",
            command=self.check_and_configure_base,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.check_config_btn.pack(side="left", padx=(0, 10))

        self.refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 Refresh Base Info",
            command=self.refresh_base_info,
            height=50,
            width=150,
            fg_color="transparent",
            border_width=2
        )
        self.refresh_btn.pack(side="left", padx=10)

        # Results section
        self.results_frame = ctk.CTkFrame(frame)
        self.results_frame.pack(fill="both", expand=True, padx=20, pady=10)

        results_title = ctk.CTkLabel(
            self.results_frame,
            text="Configuration Results",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10), anchor="w", padx=20)

        self.results_text = ctk.CTkTextbox(
            self.results_frame,
            height=200,
            font=ctk.CTkFont(size=12)
        )
        self.results_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
    def create_generate_tab(self):
        """Create the generate configuration tab"""
        frame = ctk.CTkScrollableFrame(self.content_frame)
        self.tab_frames["generate"] = frame

        # Header
        header_frame = ctk.CTkFrame(frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        header_title = ctk.CTkLabel(
            header_frame,
            text="Generate Configuration",
            font=ctk.CTkFont(size=22, weight="bold")
        )
        header_title.pack(pady=(15, 5))

        header_desc = ctk.CTkLabel(
            header_frame,
            text="Generate client configuration files for the Attribution Dashboard",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        header_desc.pack(pady=(0, 15))

        # Configuration preview
        preview_frame = ctk.CTkFrame(frame)
        preview_frame.pack(fill="both", expand=True, padx=20, pady=10)

        preview_title = ctk.CTkLabel(
            preview_frame,
            text="Configuration Preview",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        preview_title.pack(pady=(15, 10), anchor="w", padx=20)

        self.config_preview = ctk.CTkTextbox(
            preview_frame,
            height=400,
            font=ctk.CTkFont(family="Courier", size=11)
        )
        self.config_preview.pack(fill="both", expand=True, padx=20, pady=(0, 15))

        # Generate button
        generate_frame = ctk.CTkFrame(frame)
        generate_frame.pack(fill="x", padx=20, pady=10)

        buttons_frame = ctk.CTkFrame(generate_frame, fg_color="transparent")
        buttons_frame.pack(pady=20)

        self.generate_btn = ctk.CTkButton(
            buttons_frame,
            text="📊 Generate Configuration",
            command=self.generate_configuration,
            height=50,
            width=200,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.generate_btn.pack(side="left", padx=10)

        self.save_config_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 Save Configuration",
            command=self.save_configuration,
            height=50,
            width=180,
            fg_color="transparent",
            border_width=2
        )
        self.save_config_btn.pack(side="left", padx=10)

        # Second row of buttons
        buttons_frame2 = ctk.CTkFrame(generate_frame, fg_color="transparent")
        buttons_frame2.pack(pady=(0, 20))

        self.export_btn = ctk.CTkButton(
            buttons_frame2,
            text="📤 Export Settings",
            command=self.export_settings,
            height=40,
            width=150,
            fg_color="transparent",
            border_width=2
        )
        self.export_btn.pack(side="left", padx=10)

        self.import_btn = ctk.CTkButton(
            buttons_frame2,
            text="📥 Import Settings",
            command=self.import_settings,
            height=40,
            width=150,
            fg_color="transparent",
            border_width=2
        )
        self.import_btn.pack(side="left", padx=10)

        self.copy_config_btn = ctk.CTkButton(
            buttons_frame2,
            text="📋 Copy to Clipboard",
            command=self.copy_to_clipboard,
            height=40,
            width=150
        )
        self.copy_config_btn.pack(side="left", padx=10)
        
    def create_logs_tab(self):
        """Create the logs tab"""
        frame = ctk.CTkFrame(self.content_frame)
        self.tab_frames["logs"] = frame

        # Configure frame grid
        frame.grid_columnconfigure(0, weight=1)
        frame.grid_rowconfigure(1, weight=1)

        # Header
        header_frame = ctk.CTkFrame(frame)
        header_frame.grid(row=0, column=0, sticky="ew", padx=20, pady=20)

        header_title = ctk.CTkLabel(
            header_frame,
            text="Application Logs",
            font=ctk.CTkFont(size=22, weight="bold")
        )
        header_title.pack(pady=(15, 5))

        header_desc = ctk.CTkLabel(
            header_frame,
            text="View application logs and debug information",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        header_desc.pack(pady=(0, 15))

        # Logs display
        logs_frame = ctk.CTkFrame(frame)
        logs_frame.grid(row=1, column=0, sticky="nsew", padx=20, pady=(0, 20))
        logs_frame.grid_columnconfigure(0, weight=1)
        logs_frame.grid_rowconfigure(0, weight=1)

        self.logs_text = ctk.CTkTextbox(
            logs_frame,
            font=ctk.CTkFont(family="Courier", size=11)
        )
        self.logs_text.grid(row=0, column=0, sticky="nsew", padx=15, pady=15)

        # Control buttons
        controls_frame = ctk.CTkFrame(logs_frame, fg_color="transparent")
        controls_frame.grid(row=1, column=0, sticky="ew", padx=15, pady=(0, 15))

        clear_btn = ctk.CTkButton(
            controls_frame,
            text="🗑️ Clear Logs",
            command=self.clear_logs,
            width=120,
            height=35
        )
        clear_btn.pack(side="left", padx=5)

        refresh_btn = ctk.CTkButton(
            controls_frame,
            text="🔄 Refresh",
            command=self.refresh_logs,
            width=120,
            height=35,
            fg_color="transparent",
            border_width=2
        )
        refresh_btn.pack(side="left", padx=5)

    def create_settings_tab(self):
        """Create the settings tab"""
        frame = ctk.CTkScrollableFrame(self.content_frame)
        self.tab_frames["settings"] = frame

        # Header
        header_frame = ctk.CTkFrame(frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        header_title = ctk.CTkLabel(
            header_frame,
            text="Application Settings",
            font=ctk.CTkFont(size=22, weight="bold")
        )
        header_title.pack(pady=(15, 5))

        header_desc = ctk.CTkLabel(
            header_frame,
            text="Configure application preferences and behavior",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        header_desc.pack(pady=(0, 15))

        # Appearance settings
        appearance_frame = ctk.CTkFrame(frame)
        appearance_frame.pack(fill="x", padx=20, pady=10)

        appearance_title = ctk.CTkLabel(
            appearance_frame,
            text="Appearance",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        appearance_title.pack(pady=(15, 10), anchor="w", padx=20)

        # Theme selection
        theme_selection_frame = ctk.CTkFrame(appearance_frame, fg_color="transparent")
        theme_selection_frame.pack(fill="x", padx=20, pady=(0, 10))

        theme_label = ctk.CTkLabel(
            theme_selection_frame,
            text="Color Theme:",
            font=ctk.CTkFont(size=12)
        )
        theme_label.pack(side="left", padx=(0, 10))

        self.theme_option = ctk.CTkOptionMenu(
            theme_selection_frame,
            values=["blue", "green", "dark-blue"],
            command=self.change_color_theme,
            width=120
        )
        self.theme_option.pack(side="left")
        self.theme_option.set("blue")

        # Auto-save settings
        autosave_frame = ctk.CTkFrame(frame)
        autosave_frame.pack(fill="x", padx=20, pady=10)

        autosave_title = ctk.CTkLabel(
            autosave_frame,
            text="Auto-Save",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        autosave_title.pack(pady=(15, 10), anchor="w", padx=20)

        self.autosave_switch = ctk.CTkSwitch(
            autosave_frame,
            text="Auto-save configurations",
            font=ctk.CTkFont(size=12)
        )
        self.autosave_switch.pack(anchor="w", padx=20, pady=(0, 15))
        self.autosave_switch.select()  # Enable by default

        # Connection settings
        connection_frame = ctk.CTkFrame(frame)
        connection_frame.pack(fill="x", padx=20, pady=10)

        connection_title = ctk.CTkLabel(
            connection_frame,
            text="Connection",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        connection_title.pack(pady=(15, 10), anchor="w", padx=20)

        # Timeout setting
        timeout_frame = ctk.CTkFrame(connection_frame, fg_color="transparent")
        timeout_frame.pack(fill="x", padx=20, pady=(0, 10))

        timeout_label = ctk.CTkLabel(
            timeout_frame,
            text="Connection Timeout (seconds):",
            font=ctk.CTkFont(size=12)
        )
        timeout_label.pack(side="left", padx=(0, 10))

        self.timeout_entry = ctk.CTkEntry(
            timeout_frame,
            width=80,
            placeholder_text="15"
        )
        self.timeout_entry.pack(side="left")
        self.timeout_entry.insert(0, "15")

        # Advanced settings
        advanced_frame = ctk.CTkFrame(frame)
        advanced_frame.pack(fill="x", padx=20, pady=10)

        advanced_title = ctk.CTkLabel(
            advanced_frame,
            text="Advanced",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        advanced_title.pack(pady=(15, 10), anchor="w", padx=20)

        self.debug_switch = ctk.CTkSwitch(
            advanced_frame,
            text="Enable debug logging",
            font=ctk.CTkFont(size=12)
        )
        self.debug_switch.pack(anchor="w", padx=20, pady=(0, 10))

        self.verbose_switch = ctk.CTkSwitch(
            advanced_frame,
            text="Verbose output",
            font=ctk.CTkFont(size=12)
        )
        self.verbose_switch.pack(anchor="w", padx=20, pady=(0, 15))

        # Reset settings button
        reset_frame = ctk.CTkFrame(frame)
        reset_frame.pack(fill="x", padx=20, pady=10)

        reset_btn = ctk.CTkButton(
            reset_frame,
            text="🔄 Reset to Defaults",
            command=self.reset_settings,
            height=40,
            fg_color=("#d63031", "#e74c3c"),
            hover_color=("#b71c1c", "#c0392b")
        )
        reset_btn.pack(pady=20)
        
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = ModernAttributionDashboardCustomizer()
    app.run()
